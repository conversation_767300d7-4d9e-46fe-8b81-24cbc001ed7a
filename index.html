<!doctype html>
<html prefix="og: http://ogp.me/ns#" lang=en>

<head>
    <script>performance.mark("start-of-head");</script>
    <title>To lazyload or not to lazyload above the fold images | <PERSON></title>
    <meta charset=utf-8>
    <meta name=viewport
        content="width=device-width, user-scalable=yes, shrink-to-fit=yes, initial-scale=1, minimum-scale=1">
    <link rel=canonical href="https://www.erwinhofman.com/blog/to-lazyload-above-the-fold-images/">
    <link rel=apple-touch-icon href="https://www.erwinhofman.com/file/img/icon/apple-touch-icon.png" sizes=180x180>
    <link rel=icon href="https://www.erwinhofman.com/file/img/icon/favicon-32x32.png" sizes=32x32>
    <link rel=icon href="https://www.erwinhofman.com/file/img/icon/favicon-16x16.png" sizes=16x16>
    <link rel=mask-icon href="https://www.erwinhofman.com/file/img/icon/safari-pinned-tab.svg" color="#377dff">
    <link rel=manifest href="/manifest.json" crossorigin=use-credentials>
    <link rel="shortcut icon" href="https://www.erwinhofman.com/file/img/icon/favicon.ico">
    <link rel=alternate href="/rss.xml" type="application/rss+xml" title="RSS feed">
    <link rel=preconnect href="https://d5yoctgpv4cpx.cloudfront.net/">
    <link rel=preload
        href="https://www.erwinhofman.com/file/css/fonts/nunito-sans-v5-latin/nunito-sans-v5-latin-700.woff2"
        onload="document.documentElement.classList.add('wf-preloaded');" as=font type="font/woff2" crossorigin>
    <link rel=none
        href="https://www.erwinhofman.com/file/css/fonts/nunito-sans-v5-latin/nunito-sans-v5-latin-regular.woff2"
        data-font-family="Nunito Sans" data-font-style=normal data-font-weight=400 as=font type="font/woff2"
        crossorigin>
    <link rel=none
        href="https://www.erwinhofman.com/file/css/fonts/nunito-sans-v5-latin/nunito-sans-v5-latin-italic.woff2"
        data-font-family="Nunito Sans" data-font-style=italic data-font-weight=400 as=font type="font/woff2"
        crossorigin>
    <link rel=none href="https://www.erwinhofman.com/file/css/fonts/nunito-sans-v5-latin/nunito-sans-v5-latin-600.woff2"
        data-font-family="Nunito Sans" data-font-style=normal data-font-weight=600 as=font type="font/woff2"
        crossorigin>
    <meta name=theme-color content="#377dff">
    <meta name=msapplication-TileColor content="#377dff">
    <meta name=msapplication-config content="/file/img/icon/browserconfig.xml">
    <meta name=application-name content="Erwin Hofman [sitespeed consultant]">
    <meta name=apple-mobile-web-app-title content="Erwin Hofman [sitespeed consultant]">
    <meta name=keywords content="images,lazyloading,core web vitals,LCP">
    <meta name=description
        content="Independent website speed and performance consultant / specialist, Core Web Vitals included">
    <meta name="twitter:card" content=summary>
    <meta property="og:type" content=website>
    <meta property="og:site_name" content="Erwin Hofman [sitespeed consultant]">
    <meta property="og:locale" content=en>
    <meta property="og:title" content="To lazyload or not to lazyload above the fold images">
    <meta property="og:url" content="https://www.erwinhofman.com/blog/to-lazyload-above-the-fold-images/">
    <meta property="og:description"
        content="Independent website speed and performance consultant / specialist, Core Web Vitals included">
    <meta property="og:image" content="https://www.erwinhofman.com/file/upload/img/blog/image-lazyloading.jpg">
    <link href="https://www.erwinhofman.com/file/min/d1537d173f5f98038e8d02b36a43066c.css" rel=stylesheet media=all>
    <script defer src="https://www.erwinhofman.com/file/cdn/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script defer src="https://www.erwinhofman.com/file/min/1aaf28b0aec420ebf03598305225278d.js"></script><noscript>
        <style type="text/css">
            html body .no-js-hidden {
                display: none !important;
            }

            html body .no-js-block {
                display: block !important;
                opacity: 1 !important;
            }

            .js-only {
                opacity: 0.3;
            }

            .js-only:before {
                background-color: #fff;
            }

            html body .no-js-block,
            img[onload][style*=opacity],
            html body .js-enable:after,
            html body .js-only:after {
                opacity: 1 !important;
            }
        </style>
    </noscript>
    <script>performance.measure("end-of-head", "start-of-head");</script>
    <style></style><noscript></noscript>
</head>

<body class="c8192 tpl-blog blog">
    <script>var fbe = {}; window.rumv = window.rumv || function () { (window.rumv.q = window.rumv.q || []).push(arguments) }; var bodyConfig = JSON.parse(sessionStorage.getItem('config') || null) || null; if (bodyConfig) { if (bodyConfig.darkContrast) { document.body.classList.add('darkContrast'); } if (bodyConfig.wfClasses) { for (var prop in bodyConfig.wfClasses) { if (!bodyConfig.wfClasses.hasOwnProperty(prop)) { continue; } document.documentElement.classList.add(bodyConfig.wfClasses[prop]); } } }</script>
    <input type=checkbox class="js-remove sr-only" id=nav-open name="nav_open" value=1>
    <div id=top></div>
    <ul class="jump-to list-unstyled">
        <li><a href="#flex" class="sr-only sr-only-focusable btn-primary d-block py-2 px-3" id=jump-flex>to content</a>
        </li>
        <li><a href="#footer" class="sr-only sr-only-focusable btn-primary d-block py-2 px-3" id=jump-footer>to
                footer</a></li>
        <li><a href="#imported-5540" class="sr-only sr-only-focusable btn-primary d-block py-2 px-3"
                id=jump-imported-5540>to Accessibility preferences</a></li>
    </ul>
    <header>
        <div class=container><a href="/" id=logo><img class=logo elementtiming=logo
                    src="https://www.erwinhofman.com/file/img/logo.svg" alt="Erwin Hofman logo" width=64 height=64><span
                    class="hidden-xs-down text-dark no-critical">Erwin Hofman</span></a>
            <div class=roles>
                <nav itemscope itemtype="http://schema.org/SiteNavigationElement" class=nav><a href="#nav" id=toggle-nav
                        aria-label=navigation role=button><em></em></a><label for=nav-open aria-label=navigation
                        class="js-remove sr-only" tabindex=0></label>
                    <ul class=list-unstyled id=nav>
                        <li><a href="/">Home</a></li>
                        <li><a href="/erwin-hofman/">About me</a></li>
                        <li class=dropdown><a role=button href="#" id=subnav-5622 data-toggle=dropdown
                                aria-haspopup=true aria-expanded=false
                                data-href="/independent-webshop-performance-audit/">Services <b
                                    class="caret darkContrast-ignore ml-1"></b></a>
                            <div class=dropdown-menu aria-labelledby=subnav-5622>
                                <div class="menu-wrapper one-col">
                                    <div class="px-0 dropdown-products">
                                        <ul>
                                            <li><a
                                                    href="/quick-core-web-vitals-consult-or-second-opinion-on-your-pagespeed/"><span
                                                        class="fa fa-bolt fa-fw text-danger"> <svg>
                                                            <use xlink:href="/file/img/icon/svg/sprite.svg#bolt"></use>
                                                        </svg></span><strong>Quick scan or
                                                        consult</strong><br><small>Pick my brain or need a 2nd
                                                        opinion?</small></a></li>
                                            <li><a href="/webshop-pagespeed-audit-extended/"><span
                                                        class="fa fa-fw fa-check-square-o text-primary"> <svg>
                                                            <use
                                                                xlink:href="/file/img/icon/svg/sprite.svg#check-square-o">
                                                            </use>
                                                        </svg></span><strong>Pagespeed audit</strong><br><small>Helps
                                                        prioritizing Core Web Vitals and UX</small></a></li>
                                            <li><a href="/inhouse-pagespeed-training-session/"><span
                                                        class="fa fa-fw fa-graduation-cap text-success"> <svg>
                                                            <use
                                                                xlink:href="/file/img/icon/svg/sprite.svg#graduation-cap">
                                                            </use>
                                                        </svg></span><strong>Pagespeed
                                                        training</strong><br><small>Performance &amp; Core Web Vitals to
                                                        third parties</small></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li><a href="/google-core-web-vitals-pagespeed/">Core Web Vitals</a></li>
                        <li><a class=current href="/blog/" aria-current=page>Blog</a></li>
                        <li><a href="/hire-web-consultant/">Get in touch</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    <div id=flex>
        <main class=mt-0>
            <div class="vh-100 vh-md-70 w-100 pos-absolute polygon-right-bottom"><img width=1440 height=961
                    class="w-100 h-100 img-cover   no-lazy pos-absolute img-jpg pos-absolute no-js-hidden"
                    src="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA+Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2NjIpLCBkZWZhdWx0IHF1YWxpdHkK/9sAQwAIBgYHBgUIBwcHCQkICgwUDQwLCwwZEhMPFB0aHx4dGhwcICQuJyAiLCMcHCg3KSwwMTQ0NB8nOT04MjwuMzQy/9sAQwEJCQkMCwwYDQ0YMiEcITIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIy/8AAEQgANQBQAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A9YpcU7FGK6jkEpGpxpjU0BHjJqK8kEUDH2qYday9ZkK27Y9Kic+U1owuzh9ZuvNuCPerWkw9DWNPl7w59a6jSosRinB3FW0djXiXCipRSIOKfitDA3aDTd1G6oNAbpWbd3fk960GPFZt3bebQBUj1Qs2N1Nv5vPhI9qYun7Gzipmt+MGsZ03JnRTqqCOT/s4tcbtvet+xg8tAMVZFqoOcVMqBelbxjZHNOXMxwHFFLSUyDU3UbjUeaM0irjy1Rs1BNRsaAEY1ExpxNMNMQlNp1NNABSUGkoEaFIaKKChpqNqKKBDDSUUUAJTTRRQA00lFFAj/9k="
                    alt="">
                <picture>
                    <source srcset="https://www.erwinhofman.com/file/upload/img/blog/thumb.image-lazyloading.jpg.webp"
                        media="(max-width: 650px)"><img
                        src="https://www.erwinhofman.com/file/upload/img/blog/image-lazyloading.jpg.webp"
                        class="w-100 h-100 img-cover   no-lazy pos-absolute img-jpg"
                        alt="To lazyload or not to lazyload above the fold images" elementtiming=hero-shown width=1440
                        height=961 style="z-index:1;opacity:0;transition:opacity .2s" onload="this.style.opacity=1">
                </picture>
            </div>
            <article class=z-index itemscope itemtype="https://schema.org/BlogPosting">
                <div class="container z-index-3 vh-70">
                    <div class=row>
                        <section class="col-xs-12 col-lg-10 col-xl-8 offset-lg-1 offset-xl-2">
                            <div class="panel panel-body px-8 mb-5 mt-10">
                                <h1 elementtiming=h1 class="text-center text-balance" itemprop=name>To lazyload or not
                                    to lazyload above the fold images</h1>
                                <ul class="meta list-unstyled clearfix pt-3 no-critical">
                                    <li class="divider divider-fade mb-5">
                                    <li itemprop=author itemscope itemtype="http://schema.org/Person"
                                        class="author ellipsis"><noscript><img
                                                src="https://www.erwinhofman.com/file/upload/user/1/5d98ec0427152056397e9e35f357be87.jpg"
                                                width=200 height=200 class="img-circle pull-xs-left mt-1"
                                                alt="Erwin  Hofman"></noscript>
                                        <picture>
                                            <source
                                                srcset="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='200'%20height='200'%20viewBox='0%200%20200%20200'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                                                data-srcset="https://www.erwinhofman.com/file/upload/user/1/5d98ec0427152056397e9e35f357be87.jpg.webp"
                                                type="image/webp"><img
                                                class="img-circle pull-xs-left mt-1 img-jpg no-js-hidden lazyload"
                                                src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='200'%20height='200'%20viewBox='0%200%20200%20200'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                                                alt="Erwin  Hofman"
                                                data-src="https://www.erwinhofman.com/file/upload/user/1/5d98ec0427152056397e9e35f357be87.jpg"
                                                width=200 height=200 loading=lazy decoding=async>
                                        </picture><span itemprop=name>Erwin Hofman</span><br><a rel="external noopener"
                                            href="https://www.linkedin.com/in/erwinhofman/"><span
                                                class="fa fa-linkedin-square fa-fw" aria-hidden=true><svg>
                                                    <use xlink:href="/file/img/icon/svg/sprite.svg#linkedin-square">
                                                    </use>
                                                </svg></span> LinkedIn</a>
                                    </li>
                                    <li class="date label text-default"><span class="fa fa-calendar fa-fw"
                                            aria-hidden=true><svg>
                                                <use xlink:href="/file/img/icon/svg/sprite.svg#calendar"></use>
                                            </svg></span><time itemprop=datePublished datetime=2021-07-16><span
                                                class=hidden-xs-down>july 16, 2021</span><span
                                                class=hidden-sm-up>16/07/2021</span></time></li>
                                    <li class="duration label text-default"><span class="fa fa-hourglass-end fa-fw"
                                            aria-hidden=true><svg>
                                                <use xlink:href="/file/img/icon/svg/sprite.svg#hourglass-end"></use>
                                            </svg></span> &plusmn; 5 minutes</li>
                                    <li class=list-tags><a rel=nofollow class="label text-default bg-light"
                                            href="/blog/?tag=images">images</a> <a rel=nofollow
                                            class="label text-default bg-light"
                                            href="/blog/?tag=lazyloading">lazyloading</a> <a rel=nofollow
                                            class="label text-default bg-light" href="/blog/?tag=core-web-vitals">core
                                            web vitals</a> <a rel=nofollow class="label text-default bg-light"
                                            href="/blog/?tag=lcp">LCP</a></li>
                                </ul>
                            </div>
                        </section>
                    </div>
                </div>
                <div class="bg-white pt-5 polygon-right-top relative">
                    <div class=container>
                        <div class="row row-eq-height">
                            <div itemprop=articleBody
                                class="relative col-xs-12 col-lg-10 col-xl-8 offset-lg-1 offset-xl-2">
                                <div class=lead>
                                    <p class=mb-0>Wether you're reading a LinkedIn or Twitter discussion, there still is
                                        confusion around lazyloading images. Especially when it comes to above the fold
                                        images, such as hero images. Let's deal with this for once and for all.</p>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-xs-12 col-md-10 col-lg-8 offset-md-1 offset-lg-2">
                                        <div class="panel panel-body">
                                            <p class=lead>Table of Contents</p>
                                            <ul class="pl-4 mb-0">
                                                <li><a href="#above-the-fold">Above the fold images shouldn't be
                                                        lazyloaded</a>
                                                    <ul class="pl-4 py-2 font-size-1">
                                                        <li><a href="#hero-images">Hero images</a></li>
                                                        <li><a href="#product-images">Product images</a></li>
                                                        <li><a href="#company-logo">Company logo</a></li>
                                                    </ul>
                                                    <hr class="divider divider-fade mt-2 hidden-sm-down">
                                                </li>
                                                <li><a href="#above-the-fold-2">Above the fold image lazyloading
                                                        questions</a>
                                                    <ul class="pl-4 py-2 font-size-1">
                                                        <li><a href="#native-lazyloading">Native lazyloading of LCP
                                                                images</a></li>
                                                        <li><a href="#no-harm-in-lazyloading">No harm in lazyloading for
                                                                in-viewport images?</a></li>
                                                        <li><a href="#does-lazyloading">Does lazyloading even work above
                                                                the fold?</a></li>
                                                    </ul>
                                                    <hr class="divider divider-fade mt-2 hidden-sm-down">
                                                </li>
                                                <li><a href="#native-browser">Native browser lazyloading explained</a>
                                                    <ul class="pl-4 py-2 font-size-1">
                                                        <li><a href="#browser-characteristics">Browser
                                                                characteristics</a></li>
                                                        <li><a href="#native-versus">Native versus JavaScript
                                                                lazyloading</a></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <p>Let's get straight to the point:</p>
                                <h2 id=above-the-fold>Above the fold images shouldn't be lazyloaded</h2>
                                <p>Above the fold images shouldn't be lazyloaded. Crystal clear, right? But as there
                                    could be multiple images above the fold, you might wonder which exact images
                                    shouldn't be lazyloaded. This depends on the page type and template design.</p>
                                <h3 id=hero-images>Hero images</h3>
                                <p>Hero images are often used on homepages or blog- and article pages. On a homepage, a
                                    template could consist of multiple images, especially within e-commerce websites.
                                    The largest image shouldn't be lazyloaded. Other images could be lazyload to prevent
                                    resource competition amongst above the fold images.</p>
                                <h3 id=product-images>Product images</h3>
                                <p>Chances are your product detail page is using an image slider. For example via
                                    libraries such as owl carousel, tinyslider, flickity or more platform specific:
                                    Fotorama (Magento 2).</p>
                                <p>This means there are multiple images above the fold. However, only one image is
                                    really visible at a time, if we ignore the image thumbnails. Within such page
                                    template setup, you should lazyload all slideshow images, except the first. Makes
                                    sense, as the first image should be displayed as soon as possible, without a delay.
                                    And preferably without competition.</p>
                                <h3 id=company-logo>Company logo</h3>
                                <p>Some websites are using plugins to lazyload all images. And sometimes developers will
                                    just implement lazyloading to all images, even the logo, as they heard lazyloading
                                    images is the number one optimization that should be applied to all images.</p>
                                <p>However, from a commercial or just branding perspective, I wouldn't advice to
                                    lazyload the logo. Next to a product title or product image, the logo might be as
                                    important. For example when users like to shop at a specific brand, or when your
                                    company is well-known for its service, low prices or any other unique selling
                                    points.</p>
                                <p>So, don't lazyload your logo. And if you really think lazyloading your logo is
                                    worthwhile, chances are you've got bigger pagespeed fish to fry. In that case, <a
                                        href="/hire-web-consultant/">do get in touch</a> to find out.</p>
                                <h2 id=above-the-fold-2>Above the fold image lazyloading questions</h2>
                                <p>But why should we stop lazyloading above the fold images, if browsers just introduced
                                    native lazyloading? A <a href="https://web.dev/lcp-lazy-loading/"
                                        rel="external noopener">web.dev article</a> is explaining this already. This
                                    article was the result of Wordpress plugins lazyloading all images, which actually
                                    proved to be an anti-pattern when doing so for above the fold or just LCP images.
                                </p>
                                <p>This and other questions is what I sometimes run into when seeing discussions or
                                    starting lazyloading image discussions myself.</p>
                                <h3 id=native-lazyloading>Native lazyloading of LCP images</h3>
                                <blockquote>
                                    <p>I think with native lazy load you can safely do it for all images, including LCP.
                                        The browser will determine if the image needs to be lazy-loaded or not</p>
                                    <cite>An incorrect statement on LinkedIn</cite>
                                </blockquote>
                                <p>The above is something people might think: it is safe to lazyloading above the fold
                                    images, such as LCP images.</p>
                                <p>However, only the latter is correct: the browser will determine this, which will take
                                    time, resulting in potential noticeable delay (keep reading if you're interested in
                                    the nuance).</p>
                                <h3 id=no-harm-in-lazyloading>No harm in lazyloading for in-viewport images?</h3>
                                <p>The following basically boils down to the same question, as we're basically always
                                    referring to in-viewport images when talking about above-the-fold images. The
                                    question below illustrates that the characteristics of how a browser works is
                                    important as well to know how your frontend code could be impacting performance and
                                    UX.</p>
                                <blockquote>
                                    <p>Is it correct that loading=lazy immediately loads that image if it's in the
                                        viewport?</p><cite>Someone on LinkedIn wondering if browsers will do the heavy
                                        lifting</cite>
                                </blockquote>
                                <h3 id=does-lazyloading>Does lazyloading even work above the fold?</h3>
                                <blockquote>
                                    <p>Does lazyloading even work above the fold?</p><cite>Another question on
                                        Linkedin</cite>
                                </blockquote>
                                <p>Enough with the examples of image lazyloading questions, as they're basically all
                                    illustrating the same: it's time to explain what's going on in browsers' minds.</p>
                                <h2 id=native-browser>Native browser lazyloading explained</h2>
                                <p>Let's stick with "Does lazyloading even work above the fold?". Well yes, it actually
                                    works a bit too good. So, when you're applying the loading=lazy attribute to your
                                    above the fold hero-image, it will actually work and will delay the download of your
                                    image.</p>
                                <p>If you're really in search of improving your hero image LCP, see this <a
                                        href="/blog/hack-pagespeed-lcp-metric-by-enlarging-hero-image/">image LCP
                                        hack</a>. But if that doesn't fit your website design, then stop lazyloading the
                                    image.</p>
                                <h3 id=browser-characteristics>Browser characteristics</h3>
                                <p>You might know that the image will appear above the fold. Maybe because you're the
                                    designer, the developer or because you've visited the website before and now
                                    recognizing the template. But the browser doesn't know. And that's because the
                                    browser first has to do several tasks:</p>
                                <ol>
                                    <li>download the CSS (or fetch it from the browser cache);</li>
                                    <li>construct the <a
                                            href="https://css-tricks.com/an-introduction-and-guide-to-the-css-object-model-cssom/"
                                            rel="external noopener">CSS Object Model</a> and doing layout work;</li>
                                    <li>to only then being able to determine the position within the page;</li>
                                    <li>check your internet connection type to determine the <a
                                            href="https://source.chromium.org/chromium/chromium/src/+/master:third_party/blink/renderer/core/frame/settings.json5;drc=e8f3cf0bbe085fee0d1b468e84395aad3ebb2cad;l=971-1003"
                                            rel="external noopener">distance-from-viewport</a> thresholds;</li>
                                    <li>and only then it will start to download your LCP image.</li>
                                </ol>
                                <h3 id=native-versus>Native versus JavaScript lazyloading</h3>
                                <p>Obviously, the steps above will be faster then when showing the LCP would depend on
                                    JS, as it would then rely on yet another resource. And JavaScript work is more CPU
                                    heavy, so experiences might result in a bigger UX gap.</p>
                                <p>Moreover, when using native lazyloading, browsers might start to fetch images right
                                    away when they're in browser cache already. As a result, you might not notice real
                                    differences yourselves in image loading experiences with and without lazyloading
                                    them.</p>
                                <p>But your real audience won't all be browsing under the same conditions, such as
                                    installed plugins, internet connectivity, device type (memory, battery level and
                                    potential CPU throttling, temperature, et cetera) and even the page type. The delay
                                    (and thus negative impact) will be bigger during unique pageviews,. Unfortunately
                                    something that your Core Web Vitals data alone won't tell you.</p>
                                <p>That's why using lazyloading for your above the fold images is considered an
                                    anti-pattern. Especially users in the long tail experiences might notice this by a
                                    lot, but you want them to convert as well! So, go check your templates and revert
                                    lazyloading of your above-the-fold images.</p>
                                <div class="alert alert-info">
                                    <p>September 22nd update: <a
                                            href="https://github.com/GoogleChrome/lighthouse/releases/tag/v8.4.0"
                                            rel="external noopener">Lighthouse 8.4</a> as well as PageSpeed Insights
                                        will now also report when image LCP candidates were lazyloaded, possibly
                                        negatively impacting LCP.</p>
                                </div>
                            </div>
                            <div class="col-xs-12 col-lg-1 pull-lg-11 pull-xl-9 pt-2 no-critical">
                                <div class="sticky mb-3">
                                    <div class=social-share>
                                        <ul class="list-inline mb-0">
                                            <li><a data-share="http://www.linkedin.com/shareArticle?&amp;url=:url&amp;title=:title"
                                                    rel="external noopener" title="delen via LinkedIn"
                                                    href="https://www.linkedin.com/shareArticle?&amp;url=https%3A%2F%2Fwww.erwinhofman.com%2Fblog%2Fto-lazyload-above-the-fold-images%2F&amp;title=To%20lazyload%20or%20not%20to%20lazyload%20above%20the%20fold%20images"><span
                                                        class="fa fa-linkedin fa-fw" aria-hidden=true><svg>
                                                            <use xlink:href="/file/img/icon/svg/sprite.svg#linkedin">
                                                            </use>
                                                        </svg></span><span class=hidden> LinkedIn</span></a></li>
                                            <li><a data-share="http://www.facebook.com/share.php?u=:url"
                                                    rel="external noopener" title="delen via Facebook"
                                                    href="https://www.facebook.com/share.php?u=https%3A%2F%2Fwww.erwinhofman.com%2Fblog%2Fto-lazyload-above-the-fold-images%2F"><span
                                                        class="fa fa-facebook fa-fw" aria-hidden=true><svg>
                                                            <use xlink:href="/file/img/icon/svg/sprite.svg#facebook">
                                                            </use>
                                                        </svg></span><span class=hidden> Facebook</span></a></li>
                                            <li><a data-share="http://twitter.com/share?url=:url&amp;hashtags=nuon&amp;text=:title"
                                                    rel="external noopener" title="delen via Twitter"
                                                    href="https://twitter.com/share?url=https%3A%2F%2Fwww.erwinhofman.com%2Fblog%2Fto-lazyload-above-the-fold-images%2F&amp;hashtags=nuon&amp;text=To%20lazyload%20or%20not%20to%20lazyload%20above%20the%20fold%20images"><span
                                                        class="fa fa-twitter fa-fw" aria-hidden=true><svg>
                                                            <use xlink:href="/file/img/icon/svg/sprite.svg#twitter">
                                                            </use>
                                                        </svg></span><span class=hidden> Twitter</span></a></li>
                                            <li><a data-share="whatsapp://send?text=:url" rel="external noopener"
                                                    title="delen via WhatsApp"
                                                    href="whatsapp://send?text=https%3A%2F%2Fwww.erwinhofman.com%2Fblog%2Fto-lazyload-above-the-fold-images%2F"><span
                                                        class="fa fa-whatsapp fa-fw" aria-hidden=true><svg>
                                                            <use xlink:href="/file/img/icon/svg/sprite.svg#whatsapp">
                                                            </use>
                                                        </svg></span><span class=hidden> WhatsApp</span></a></li>
                                            <li><a data-share="mailto:?subject=:title&amp;body=:url"
                                                    rel="external noopener" title="delen via Mail"
                                                    href="mailto:?subject=To%20lazyload%20or%20not%20to%20lazyload%20above%20the%20fold%20images&amp;body=https%3A%2F%2Fwww.erwinhofman.com%2Fblog%2Fto-lazyload-above-the-fold-images%2F"><span
                                                        class="fa fa-envelope fa-fw" aria-hidden=true><svg>
                                                            <use xlink:href="/file/img/icon/svg/sprite.svg#envelope">
                                                            </use>
                                                        </svg></span><span class=hidden> Mail</span></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
            <div class="list-related pt-11 pb-6 bg-light z-index">
                <div class=container>
                    <ul class="row row-eq-height listing list-unstyled">
                        <li class="col-xs-12 col-sm-6 col-md-4">
                            <article class="window-location panel h-100">
                                <div class="hero transform"><noscript><img
                                            src="https://www.erwinhofman.com/file/upload/img/blog/thumb.regge-tegels-sfeerfoto.jpg"
                                            width=650 height=446
                                            alt="Hack your PageSpeed LCP metric by enlarging your hero image"></noscript>
                                    <picture>
                                        <source
                                            srcset="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='650'%20height='446'%20viewBox='0%200%20650%20446'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                                            data-srcset="https://www.erwinhofman.com/file/upload/img/blog/thumb.regge-tegels-sfeerfoto.jpg.webp"
                                            type="image/webp"><img
                                            src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='650'%20height='446'%20viewBox='0%200%20650%20446'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                                            alt="Hack your PageSpeed LCP metric by enlarging your hero image"
                                            elementtiming=related-blog-shown
                                            data-src="https://www.erwinhofman.com/file/upload/img/blog/thumb.regge-tegels-sfeerfoto.jpg"
                                            width=650 height=446 class="img-jpg no-js-hidden lazyload" loading=lazy
                                            decoding=async>
                                    </picture>
                                </div>
                                <div class="p-3 pb-0">
                                    <ul class="meta list-unstyled clearfix">
                                        <li class="label text-default">Related article</li>
                                    </ul>
                                    <h5 class="mt-2 pb-3"><a
                                            href="/blog/hack-pagespeed-lcp-metric-by-enlarging-hero-image/">Hack your
                                            PageSpeed LCP metric by enlarging your hero image</a></h5>
                                </div>
                            </article>
                        </li>
                        <li class="col-xs-12 col-sm-6 col-md-4">
                            <article class="window-location panel h-100">
                                <div class="hero transform"><noscript><img
                                            src="https://www.erwinhofman.com/file/upload/img/blog/thumb.cross-fingers-laptop-desk.jpg"
                                            width=650 height=433
                                            alt="Spot issues in your copied preconnect code snippet [how to]"></noscript>
                                    <picture>
                                        <source
                                            srcset="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='650'%20height='433'%20viewBox='0%200%20650%20433'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                                            data-srcset="https://www.erwinhofman.com/file/upload/img/blog/thumb.cross-fingers-laptop-desk.jpg.webp"
                                            type="image/webp"><img
                                            src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='650'%20height='433'%20viewBox='0%200%20650%20433'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                                            alt="Spot issues in your copied preconnect code snippet [how to]"
                                            data-src="https://www.erwinhofman.com/file/upload/img/blog/thumb.cross-fingers-laptop-desk.jpg"
                                            width=650 height=433 class="img-jpg no-js-hidden lazyload" loading=lazy
                                            decoding=async>
                                    </picture>
                                </div>
                                <div class="p-3 pb-0">
                                    <ul class="meta list-unstyled clearfix">
                                        <li class="label text-default">Previous article</li>
                                    </ul>
                                    <h5 class="mt-2 pb-3"><a
                                            href="/blog/spot-issues-in-preconnect-code-snippet-how-to/">Spot issues in
                                            your copied preconnect code snippet [how to]</a></h5>
                                </div>
                            </article>
                        </li>
                        <li class="col-xs-12 col-sm-6 col-md-4">
                            <article class="window-location panel h-100">
                                <div class="hero transform"><noscript><img
                                            src="https://www.erwinhofman.com/file/upload/img/blog/thumb.reddit-mobile-phone.jpg"
                                            width=650 height=412
                                            alt="Anyone else not buying Core Web Vitals?"></noscript>
                                    <picture>
                                        <source
                                            srcset="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='650'%20height='412'%20viewBox='0%200%20650%20412'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                                            data-srcset="https://www.erwinhofman.com/file/upload/img/blog/thumb.reddit-mobile-phone.jpg.webp"
                                            type="image/webp"><img
                                            src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='650'%20height='412'%20viewBox='0%200%20650%20412'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                                            alt="Anyone else not buying Core Web Vitals?"
                                            data-src="https://www.erwinhofman.com/file/upload/img/blog/thumb.reddit-mobile-phone.jpg"
                                            width=650 height=412 class="img-jpg no-js-hidden lazyload" loading=lazy
                                            decoding=async>
                                    </picture>
                                </div>
                                <div class="p-3 pb-0">
                                    <ul class="meta list-unstyled clearfix">
                                        <li class="label text-default">Next article</li>
                                    </ul>
                                    <h5 class="mt-2 pb-3"><a href="/blog/anyone-else-not-buying-core-web-vitals/">Anyone
                                            else not buying Core Web Vitals?</a></h5>
                                </div>
                            </article>
                        </li>
                    </ul>
                </div>
            </div>
        </main>
    </div>
    <nav id=breadcrumb>
        <div class=container>
            <ol class="breadcrumb list-inline clearfix" itemscope itemtype="https://schema.org/BreadcrumbList">
                <li itemprop=itemListElement itemscope itemtype="https://schema.org/ListItem"><a href="/"
                        itemprop=item><span itemprop=name>Home</span></a>
                    <meta itemprop=position content=1 />
                </li>
                <li itemprop=itemListElement itemscope itemtype="https://schema.org/ListItem"><a href="/blog/"
                        itemprop=item><span itemprop=name>Blog</span></a>
                    <meta itemprop=position content=2 />
                </li>
                <li itemprop=itemListElement itemscope itemtype="https://schema.org/ListItem"><a
                        href="/blog/to-lazyload-above-the-fold-images/" aria-current=page itemprop=item><span
                            itemprop=name>To lazyload or not to lazyload above the fold images</span></a>
                    <meta itemprop=position content=3 />
                </li>
            </ol>
        </div>
    </nav>
    <footer class="bg-dark pt-10" id=footer>
        <div class="container main py-7">
            <div class=row>
                <div class=col-lg-7>
                    <h3 class="text-secondary mb-2">Erwin Hofman, Independend site speed consultant &amp; Google
                        Developer Expert</h3>
                    <p class="lead mb-5">I helped 500+ eCommerce websites &amp; agency's become blazing fast. Let's
                        speed up together!</p><noscript><img
                            src="https://www.erwinhofman.com/file/upload/img/experts-digital-badge-logos-2023_webtechnologies.png"
                            width=210 height=75 alt="Google Developer Expert Erwin Hofman"></noscript>
                    <picture>
                        <source
                            srcset="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='600'%20height='213'%20viewBox='0%200%20600%20213'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                            data-srcset="https://www.erwinhofman.com/file/upload/img/experts-digital-badge-logos-2023_webtechnologies.png.webp"
                            type="image/webp"><img alt="Google Developer Expert Erwin Hofman" height=75
                            src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='600'%20height='213'%20viewBox='0%200%20600%20213'%20style='background-color:transparent'%20%3E%3C/svg%3E"
                            width=210
                            data-src="https://www.erwinhofman.com/file/upload/img/experts-digital-badge-logos-2023_webtechnologies.png"
                            class="img-png no-js-hidden lazyload" loading=lazy decoding=async>
                    </picture>
                </div>
                <div class="col-lg-5 text-lg-right mt-4 mt-lg-0"><a class="btn btn-primary m-1"
                        href="/webshop-pagespeed-audit-extended/"><span class="fa fa-fw fa-check-square-o"> <svg>
                                <use xlink:href="/file/img/icon/svg/sprite.svg#check-square-o"></use>
                            </svg></span> Audit</a> <a class="btn btn-success m-1"
                        href="/inhouse-pagespeed-training-session/"><span class="fa fa-fw fa-graduation-cap"> <svg>
                                <use xlink:href="/file/img/icon/svg/sprite.svg#graduation-cap"></use>
                            </svg></span> Training</a></div>
            </div>
        </div>
        <div class="container sub">
            <hr class="divider divider-fade divider-dark my-5">
            <ul class="clearfix list-unstyled pb-3">
                <li><a href="/en/sitemap/"><span class="fa fa-fw fa-sitemap"> <svg>
                                <use xlink:href="/file/img/icon/svg/sprite.svg#sitemap"></use>
                            </svg></span> Sitemap</a></li>
                <li><a href="/statements/accessibility/"><span class="fa fa-fw fa-wheelchair"> <svg>
                                <use xlink:href="/file/img/icon/svg/sprite.svg#wheelchair"></use>
                            </svg></span> Accessibility</a></li>
                <li><a href="/statements/privacy-policy/"><span class="fa fa-fw fa-lock"> <svg>
                                <use xlink:href="/file/img/icon/svg/sprite.svg#lock"></use>
                            </svg></span> Privacy policy</a></li>
                <li class=w-100><span class="fa fa-fw fa-copyright"> <svg>
                            <use xlink:href="/file/img/icon/svg/sprite.svg#copyright"></use>
                        </svg></span> 2025 | <a href="/erwin-hofman/">Erwin Hofman</a> | international &amp; independent
                    sitespeed consultant</li>
            </ul>
        </div> <svg viewbox="0 -20 700 110" width="100%" height="110" preserveaspectratio="none">
            <path transform="translate(0, -20)" d="M0,10 c80,-22 240,0 350,18 c90,17 260,7.5 350,-20 v50 h-700"></path>
            <path d="M0,10 c80,-18 230,-12 350,7 c80,13 260,17 350,-5 v100 h-700z"></path>
        </svg>
    </footer>
    <script>var saveData = 0, isSlow = (window.location.href).indexOf('slow=') >= 0 || (navigator.connection && /\slow-2g|2g|3g/.test(navigator.connection.effectiveType)), i; if (!saveData && !isSlow && 'bodyConfig' in window && (bodyConfig === null || bodyConfig.wfClasses === null)) { var fonts = document.querySelectorAll('head link[as="font"]:not([rel="preload"])'); for (i = 0; i < fonts.length; i++) { fonts[i].setAttribute('rel', 'preload'); } }</script>
    <div id=slideouts class="no-critical no-js-hidden">
        <ul class="list-unstyled list-inline no-critical m-0">
            <li class="item dropup"><a class="dropdown-toggle btn btn-primary text-lg p-2" data-toggle=dropdown
                    aria-haspopup=true aria-expanded=false id=imported-5540 href="#"><span
                        class="fa fa-moon-o jump-target fa-a11y fa-fw" aria-hidden=true><svg>
                            <use xlink:href="/file/img/icon/svg/sprite.svg#moon-o"></use>
                        </svg></span><span class=sr-only>Your preferences</span></a>
                <div class="dropdown-menu dropdown-menu-right" id=imported-5540-menu aria-labelledby=imported-5540>
                    <p><span class="checkbox d-block px-2 py-1"><input id="bodyDefaults_darkContrast"
                                name="bodyDefaults[]" type=checkbox value=darkContrast><label
                                for="bodyDefaults_darkContrast">Dark contrast</label></span><span
                            class="checkbox d-block px-2 py-1"><input id="bodyDefaults_hasContrast"
                                name="bodyDefaults[]" type=checkbox value=hasContrast><label
                                for="bodyDefaults_hasContrast">High contrast</label></span><span
                            class="checkbox d-block px-2 py-1"><input id="bodyDefaults_saveData" name="bodyDefaults[]"
                                type=checkbox value=saveData><label for="bodyDefaults_saveData">Save
                                bandwidth</label></span><span class="checkbox d-block px-2 py-1"><input
                                id="bodyDefaults_reducedMotion" name="bodyDefaults[]" type=checkbox
                                value=reducedMotion><label for="bodyDefaults_reducedMotion">Reduced
                                motions</label></span><span class="checkbox d-block px-2 py-1"><input
                                id="bodyDefaults_newWindow" name="bodyDefaults[]" type=checkbox value=newWindow><label
                                for="bodyDefaults_newWindow">Open external links in a new window</label></span></p>
                </div>
            </li>
        </ul>
    </div><a aria-label="back to content" href="#top" class="scrolltop btn btn-primary text-lg p-2"><span
            class="fa fa-arrow-up fa-fw" aria-hidden=true><svg>
                <use xlink:href="/file/img/icon/svg/sprite.svg#arrow-up"></use>
            </svg></span></a>
    <script>var jsFiles = { "chatbotjs": "\/file\/min\/chatbot.1650576593.js", "polyfillIntersectionObserverjs": "\/file\/min\/polyfill.IntersectionObserver.1592772356.js", "flickityjs": "\/file\/min\/flickity.1604186719.js", "fancyboxjs": "\/file\/min\/fancybox.1598646358.js", "buttonjs": "\/file\/min\/button.1609256227.js", "dropdownfilterjs": "\/file\/min\/dropdown-filter.1622726212.js", "exchangeratejs": "\/file\/min\/exchange-rate.1622725610.js", "tabjs": "\/file\/min\/tab.1586647679.js", "collapsejs": "\/file\/min\/collapse.1586647678.js", "formsjs": "\/file\/min\/forms.1604196979.js", "serviceworkerjs": "\/file\/min\/serviceworker.1592772356.js", "facetsearchjs": "\/file\/min\/facet-search.1639174475.js", "facetcustomjs": "\/file\/min\/facet-custom.1634082894.js", "rangesliderjs": "\/file\/min\/rangeslider.1634082892.js", "revenueroijs": "\/file\/min\/revenue-roi.1623534762.js", "transitionjs": "\/file\/min\/transition.1586647679.js", "instantpagejs": "\/file\/min\/instantpage.1693236866.js", "lazysizesjs": "\/file\/min\/lazysizes.1639174935.js" };</script>
</body>

</html>
<!--
	Loaded in 0.0028 seconds with optimal pagespeed
	Check the console to learn more or check blue2blond.nl/nieuws.html
-->